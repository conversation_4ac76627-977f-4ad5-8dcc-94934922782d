"""
Database Service Abstraction Layer.

Provides a high-level abstraction over database operations with enhanced
security, monitoring, and connection management.
"""

import logging
import asyncio
from contextlib import contextmanager, asynccontextmanager
from typing import Dict, Any, Optional, List, Callable, Type, TypeVar
from datetime import datetime, timezone

from sqlalchemy import create_engine, text, event
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.pool import QueuePool

from ..database import Base
from ..services.connection_manager import EnhancedConnectionManager
from ..utils.error_handlers import MCPError
from .base_repository import BaseRepository

logger = logging.getLogger(__name__)

# Import specific repository classes for proper instantiation
# We'll import these lazily to avoid circular imports

T = TypeVar('T', bound=Base)


class DatabaseServiceError(MCPError):
    """Database service specific error."""

    def __init__(self, message: str, operation: Optional[str] = None, **kwargs):
        # Remove operation from kwargs before passing to parent
        kwargs.pop('operation', None)
        super().__init__(message, error_code="DATABASE_SERVICE_ERROR", **kwargs)
        self.operation = operation


class DatabaseService:
    """
    Database Service providing high-level database operations.
    
    Features:
    - Connection pool management with monitoring
    - Transaction management with rollback support
    - Query auditing and performance monitoring
    - Repository factory for type-safe data access
    - Database health monitoring
    - Security enhancements with query validation
    """
    
    def __init__(
        self,
        database_url: str,
        echo: bool = False,
        pool_size: int = 20,
        max_overflow: int = 30,
        pool_timeout: int = 30,
        pool_recycle: int = 3600,
        enable_query_auditing: bool = True,
        enable_performance_monitoring: bool = True
    ):
        self.database_url = database_url
        self.echo = echo
        self.enable_query_auditing = enable_query_auditing
        self.enable_performance_monitoring = enable_performance_monitoring
        
        self.logger = logging.getLogger(__name__)
        
        # Connection management
        self.connection_manager = EnhancedConnectionManager(
            database_url=database_url,
            pool_size=pool_size,
            max_overflow=max_overflow,
            pool_timeout=pool_timeout,
            pool_recycle=pool_recycle
        )
        
        # Repository cache
        self._repository_cache: Dict[Type[T], BaseRepository[T]] = {}
        
        # Performance monitoring
        self._query_stats: Dict[str, Dict[str, Any]] = {}
        self._connection_stats = {
            "total_connections": 0,
            "active_connections": 0,
            "failed_connections": 0,
            "query_count": 0,
            "slow_queries": 0
        }
        
        # Initialize engine and session factory
        self._initialize_database()
        
        # Set up event listeners for monitoring
        if enable_performance_monitoring:
            self._setup_performance_monitoring()
        
        if enable_query_auditing:
            self._setup_query_auditing()
    
    def _initialize_database(self):
        """Initialize database engine and session factory."""
        try:
            # Enhanced connection pool configuration
            connect_args = {}
            if self.database_url.startswith("postgresql"):
                connect_args = {
                    "application_name": "datagenius_db_service",
                    "options": "-c statement_timeout=30000 -c jit=off",
                    "connect_timeout": 10
                }
            elif self.database_url.startswith("sqlite"):
                connect_args = {"check_same_thread": False}
            
            # Create engine with enhanced configuration
            self.engine = create_engine(
                self.database_url,
                echo=self.echo,
                connect_args=connect_args,
                poolclass=QueuePool,
                pool_size=20,
                max_overflow=30,
                pool_pre_ping=True,
                pool_recycle=3600,
                pool_timeout=30,
                pool_reset_on_return="commit"
            )
            
            # Create session factory
            self.SessionLocal = sessionmaker(
                autocommit=False,
                autoflush=False,
                bind=self.engine
            )
            
            self.logger.info("Database service initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize database service: {e}")
            raise DatabaseServiceError(
                "Failed to initialize database service",
                operation="initialization"
            )
    
    def _setup_performance_monitoring(self):
        """Set up performance monitoring event listeners."""
        
        @event.listens_for(self.engine, "before_cursor_execute")
        def before_cursor_execute(conn, cursor, statement, parameters, context, executemany):
            context._query_start_time = datetime.now()
            self._connection_stats["query_count"] += 1
        
        @event.listens_for(self.engine, "after_cursor_execute")
        def after_cursor_execute(conn, cursor, statement, parameters, context, executemany):
            if hasattr(context, '_query_start_time'):
                execution_time = (datetime.now() - context._query_start_time).total_seconds()
                
                # Track slow queries (> 1 second)
                if execution_time > 1.0:
                    self._connection_stats["slow_queries"] += 1
                    self.logger.warning(f"Slow query detected: {execution_time:.2f}s - {statement[:100]}...")
                
                # Update query statistics
                query_type = statement.strip().split()[0].upper()
                if query_type not in self._query_stats:
                    self._query_stats[query_type] = {
                        "count": 0,
                        "total_time": 0.0,
                        "avg_time": 0.0,
                        "max_time": 0.0
                    }
                
                stats = self._query_stats[query_type]
                stats["count"] += 1
                stats["total_time"] += execution_time
                stats["avg_time"] = stats["total_time"] / stats["count"]
                stats["max_time"] = max(stats["max_time"], execution_time)
        
        @event.listens_for(self.engine, "connect")
        def connect(dbapi_connection, connection_record):
            self._connection_stats["total_connections"] += 1
            self._connection_stats["active_connections"] += 1
        
        @event.listens_for(self.engine, "close")
        def close(dbapi_connection, connection_record):
            self._connection_stats["active_connections"] -= 1
        
        @event.listens_for(self.engine, "handle_error")
        def handle_error(exception_context):
            self._connection_stats["failed_connections"] += 1
            self.logger.error(f"Database connection error: {exception_context.original_exception}")
    
    def _setup_query_auditing(self):
        """Set up query auditing event listeners."""
        
        @event.listens_for(self.engine, "before_cursor_execute")
        def audit_query(conn, cursor, statement, parameters, context, executemany):
            # Log query for audit purposes (be careful with sensitive data)
            audit_data = {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "query_type": statement.strip().split()[0].upper(),
                "statement": statement[:200] + "..." if len(statement) > 200 else statement,
                "parameter_count": len(parameters) if parameters else 0,
                "executemany": executemany
            }
            
            self.logger.debug("AUDIT: Database query executed", extra=audit_data)
    
    @contextmanager
    def get_session(self):
        """Get a database session with automatic cleanup."""
        session = self.SessionLocal()
        try:
            yield session
        except Exception as e:
            session.rollback()
            self.logger.error(f"Database session error: {e}")
            raise
        finally:
            session.close()
    
    @asynccontextmanager
    async def get_async_session(self):
        """Get an async database session (placeholder for future async support)."""
        # For now, use sync session in async context
        # This can be enhanced with async SQLAlchemy in the future
        session = self.SessionLocal()
        try:
            yield session
        except Exception as e:
            session.rollback()
            self.logger.error(f"Async database session error: {e}")
            raise
        finally:
            session.close()
    
    def get_repository(self, model_class: Type[T], session: Optional[Session] = None) -> BaseRepository[T]:
        """
        Get a repository instance for the specified model.

        Args:
            model_class: SQLAlchemy model class
            session: Optional database session (if not provided, creates new one)

        Returns:
            Repository instance for the model
        """
        # Use provided session or create new one
        if session is None:
            session = self.SessionLocal()

        # Check cache first
        cache_key = (model_class, id(session))
        if cache_key in self._repository_cache:
            return self._repository_cache[cache_key]

        # Create appropriate repository instance based on model class
        repository = self._create_repository_instance(model_class, session)

        # Enable features based on service configuration
        repository.enable_audit_logging(self.enable_query_auditing)

        # Cache repository
        self._repository_cache[cache_key] = repository

        return repository

    def _create_repository_instance(self, model_class: Type[T], session: Session) -> BaseRepository[T]:
        """Create the appropriate repository instance for the given model class."""
        # Import specific repository classes to avoid circular imports
        from ..models.database_models import User, Conversation, BusinessProfile

        # Map model classes to their specific repository classes
        if model_class == Conversation:
            from .conversation_repository import ConversationRepository
            return ConversationRepository(session)
        elif model_class == User:
            from .user_repository import UserRepository
            return UserRepository(session)
        elif model_class == BusinessProfile:
            from .business_profile_repository import BusinessProfileRepository
            return BusinessProfileRepository(session)
        else:
            # Fall back to generic BaseRepository for other models
            return BaseRepository(session, model_class)
    
    def create_tables(self):
        """Create all database tables."""
        try:
            Base.metadata.create_all(bind=self.engine)
            self.logger.info("Database tables created successfully")
        except Exception as e:
            self.logger.error(f"Failed to create database tables: {e}")
            raise DatabaseServiceError(
                "Failed to create database tables",
                operation="create_tables"
            )
    
    def drop_tables(self):
        """Drop all database tables (use with caution)."""
        try:
            Base.metadata.drop_all(bind=self.engine)
            self.logger.warning("All database tables dropped")
        except Exception as e:
            self.logger.error(f"Failed to drop database tables: {e}")
            raise DatabaseServiceError(
                "Failed to drop database tables",
                operation="drop_tables"
            )
    
    def execute_raw_query(
        self,
        query: str,
        params: Optional[Dict[str, Any]] = None,
        session: Optional[Session] = None
    ) -> Any:
        """
        Execute a raw SQL query with security validation.
        
        Args:
            query: SQL query string
            params: Query parameters
            session: Optional database session
            
        Returns:
            Query result
        """
        try:
            # Validate query for security (basic validation)
            self._validate_query_security(query)
            
            if session:
                if params:
                    result = session.execute(text(query), params)
                else:
                    result = session.execute(text(query))
            else:
                with self.get_session() as session:
                    if params:
                        result = session.execute(text(query), params)
                    else:
                        result = session.execute(text(query))
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error executing raw query: {e}")
            raise DatabaseServiceError(
                "Failed to execute raw query",
                operation="execute_raw_query"
            )
    
    def _validate_query_security(self, query: str):
        """Basic security validation for raw queries."""
        query_upper = query.upper().strip()
        
        # Block potentially dangerous operations
        dangerous_keywords = [
            'DROP TABLE', 'DROP DATABASE', 'TRUNCATE', 'DELETE FROM',
            'ALTER TABLE', 'CREATE USER', 'GRANT', 'REVOKE'
        ]
        
        for keyword in dangerous_keywords:
            if keyword in query_upper:
                raise DatabaseServiceError(
                    f"Query contains potentially dangerous keyword: {keyword}",
                    operation="query_validation"
                )
        
        # Ensure parameterized queries
        if "'" in query and not query.count("'") % 2 == 0:
            self.logger.warning("Query may contain unescaped string literals")
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """Get database connection statistics."""
        return {
            **self._connection_stats,
            "pool_size": self.engine.pool.size(),
            "checked_in": self.engine.pool.checkedin(),
            "checked_out": self.engine.pool.checkedout(),
            "overflow": self.engine.pool.overflow(),
            "invalid": self.engine.pool.invalid()
        }
    
    def get_query_stats(self) -> Dict[str, Dict[str, Any]]:
        """Get query performance statistics."""
        return self._query_stats.copy()
    
    def health_check(self) -> Dict[str, Any]:
        """Perform database health check."""
        try:
            with self.get_session() as session:
                # Simple query to test connection
                result = session.execute(text("SELECT 1"))
                result.fetchone()
            
            return {
                "status": "healthy",
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "connection_stats": self.get_connection_stats(),
                "query_stats": self.get_query_stats()
            }
            
        except Exception as e:
            self.logger.error(f"Database health check failed: {e}")
            return {
                "status": "unhealthy",
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
    
    def cleanup(self):
        """Clean up database service resources."""
        try:
            # Clear repository cache
            self._repository_cache.clear()
            
            # Dispose of engine
            if hasattr(self, 'engine'):
                self.engine.dispose()
            
            # Cleanup connection manager
            if hasattr(self, 'connection_manager'):
                # Connection manager cleanup would go here
                pass
            
            self.logger.info("Database service cleanup completed")
            
        except Exception as e:
            self.logger.error(f"Error during database service cleanup: {e}")


# Global database service instance
_database_service: Optional[DatabaseService] = None


def get_database_service(
    database_url: Optional[str] = None,
    **kwargs
) -> DatabaseService:
    """Get the global database service instance."""
    global _database_service
    
    if _database_service is None:
        if not database_url:
            from ..config import DATABASE_URL
            database_url = DATABASE_URL
        
        _database_service = DatabaseService(database_url, **kwargs)
    
    return _database_service


def initialize_database_service(database_url: str, **kwargs) -> DatabaseService:
    """Initialize the global database service."""
    global _database_service
    
    if _database_service is not None:
        _database_service.cleanup()
    
    _database_service = DatabaseService(database_url, **kwargs)
    return _database_service

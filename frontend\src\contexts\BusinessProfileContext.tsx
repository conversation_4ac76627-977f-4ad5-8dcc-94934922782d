/**
 * Global Business Profile Context Provider
 * 
 * Provides global business profile state and change notifications to all components.
 * Wraps the application and ensures profile state is available everywhere.
 */

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { BusinessProfile } from '@/lib/businessProfileApi';
import { useBusinessProfileStore, businessProfileEventEmitter } from '@/stores/business-profile-store';

interface BusinessProfileContextType {
  // Current state
  activeProfile: BusinessProfile | null;
  profiles: BusinessProfile[];
  isLoading: boolean;
  error: string | null;

  // Actions
  switchProfile: (profileId: string) => Promise<void>;
  refreshProfiles: () => Promise<void>;
  
  // Utilities
  isProfileActive: (profileId: string) => boolean;
  getProfileById: (profileId: string) => BusinessProfile | null;
}

const BusinessProfileContext = createContext<BusinessProfileContextType | null>(null);

interface BusinessProfileProviderProps {
  children: ReactNode;
}

export const BusinessProfileProvider: React.FC<BusinessProfileProviderProps> = ({ children }) => {
  const store = useBusinessProfileStore();
  const [profileChangeCount, setProfileChangeCount] = useState(0);

  // Subscribe to profile changes to trigger re-renders
  useEffect(() => {
    const unsubscribe = businessProfileEventEmitter.subscribe((profile) => {
      console.log('🔄 Business profile changed:', profile?.name || 'None');
      setProfileChangeCount(prev => prev + 1);
    });

    return unsubscribe;
  }, []);

  // Load profiles on mount if not already loaded
  useEffect(() => {
    const shouldLoad = !store.lastUpdated || Date.now() - store.lastUpdated > 5 * 60 * 1000;
    if (shouldLoad && !store.isLoading) {
      // Load if never loaded or last update was more than 5 minutes ago, and not currently loading
      const timestamp = new Date().toISOString();
      console.log(`🔄 [BusinessProfileContext] ${timestamp} Loading profiles - lastUpdated: ${store.lastUpdated}, isLoading: ${store.isLoading}`);
      store.loadProfiles();
    }
  }, [store.lastUpdated, store.isLoading]); // Add isLoading to prevent concurrent requests

  const contextValue: BusinessProfileContextType = {
    // Current state
    activeProfile: store.activeProfile,
    profiles: store.profiles,
    isLoading: store.isLoading,
    error: store.error,

    // Actions
    switchProfile: store.switchProfile,
    refreshProfiles: store.refreshProfiles,

    // Utilities
    isProfileActive: store.isProfileActive,
    getProfileById: store.getProfileById,
  };

  return (
    <BusinessProfileContext.Provider value={contextValue}>
      {children}
    </BusinessProfileContext.Provider>
  );
};

// Hook to use the business profile context
export const useBusinessProfileContext = (): BusinessProfileContextType => {
  const context = useContext(BusinessProfileContext);
  
  if (!context) {
    throw new Error('useBusinessProfileContext must be used within a BusinessProfileProvider');
  }
  
  return context;
};

// Hook for components that need to react to profile changes
export const useBusinessProfileListener = (
  callback: (profile: BusinessProfile | null) => void,
  deps: React.DependencyList = []
) => {
  useEffect(() => {
    const unsubscribe = businessProfileEventEmitter.subscribe(callback);
    return unsubscribe;
  }, deps);
};

// Hook for getting just the active profile (most common use case)
export const useActiveBusinessProfile = (): BusinessProfile | null => {
  const { activeProfile } = useBusinessProfileContext();
  return activeProfile;
};

// Hook for profile switching with error handling
export const useBusinessProfileSwitcher = () => {
  const { switchProfile } = useBusinessProfileContext();
  const [isSwitching, setIsSwitching] = useState(false);
  const [switchError, setSwitchError] = useState<string | null>(null);

  const handleSwitchProfile = async (profileId: string) => {
    try {
      setIsSwitching(true);
      setSwitchError(null);
      await switchProfile(profileId);
    } catch (error) {
      setSwitchError(error instanceof Error ? error.message : 'Failed to switch profile');
      throw error;
    } finally {
      setIsSwitching(false);
    }
  };

  return {
    switchProfile: handleSwitchProfile,
    isSwitching,
    switchError,
    clearError: () => setSwitchError(null),
  };
};

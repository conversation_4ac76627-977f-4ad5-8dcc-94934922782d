"""
Main FastAPI application entry point.

This module initializes the FastAPI application with the new unified architecture,
including configuration service, error handling framework, and repository pattern.
"""

import logging
from contextlib import asynccontextmanager
from datetime import datetime
from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

# Import configuration and services
from . import config
from .services.configuration_service import get_configuration_service
from .config_migration import ensure_migration_complete
from .errors.error_handler_registry import get_error_registry
from .errors.correlation_context import CorrelationContext
from .repositories.database_service import get_database_service

# Import routers
from .api.chat import router as chat_router
from .api.agents import router as agents_router
from .api.files import router as files_router
from .api.auth import router as auth_router
from .api.personas import router as personas_router
from .api.providers import router as providers_router
from .api.data_sources import router as data_sources_router
from .api.cart import router as cart_router
from .api.purchases import router as purchases_router
from .api.admin import router as admin_router
from .api.health import router as health_router
from .api.models import router as models_router
from .api.document_query import router as document_query_router
from .api.docx import router as docx_router
# Dashboard metrics router removed
from .api.dashboards import router as dashboards_router
from .api.data_access import router as data_access_router
from .api.analytics_widgets import router as analytics_widgets_router
from .api.reports import router as reports_router
from .api.cross_agent_intelligence import router as cross_agent_intelligence_router
from .api.notifications_router import router as notifications_router # Changed
from .api.feedback import router as feedback_router
from .api.pricing import router as pricing_router
from .api.search import router as search_router
from .api.dashboard_customization import router as dashboard_customization_router
from .api.embedding_config import router as embedding_config_router
from .api.quick_actions import router as quick_actions_router
from .api.chunking_performance import router as chunking_performance_router
from .api.business_context import router as business_context_router
from .api.business_profiles import router as business_profiles_router
from .api.business_profile_autofill import router as business_profile_autofill_router
from .api.profile_recommendations import router as profile_recommendations_router
from .api.data_source_intelligence import router as data_source_intelligence_router
from .api.business_profile_analytics import router as business_profile_analytics_router
from .api.realtime_context import router as realtime_context_router
from .api.template_management import router as template_management_router
from .api.dashboard_websocket import router as dashboard_websocket_router, dashboard_router as dashboard_alt_router
from .api.performance import router as performance_router
from .api.enhanced_personas import router as enhanced_personas_router
from .api.mcp_servers import router as mcp_servers_router

# Import WebSocket
# Dashboard metrics WebSocket removed

# Import Unified Security Middleware
from .middleware.unified_security import UnifiedSecurityMiddleware, UNIFIED_SECURITY_CONFIG

# Import database
from .database import init_db

# Import LangGraph WorkflowManager instead of legacy orchestration
from agents.langgraph.core.workflow_manager import WorkflowManager
global_workflow_manager_instance = WorkflowManager()  # Use LangGraph workflow manager


# Configure logging
logging.basicConfig(
    level=logging.DEBUG if config.DEBUG else logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Background task functions for non-blocking startup
async def sync_personas_background(persona_manager, db):
    """Background task to synchronize personas with database."""
    try:
        synchronized_personas = persona_manager.sync_personas_with_database(db)
        logger.info(f"Background: Synchronized {len(synchronized_personas)} personas with database: {synchronized_personas}")
    except Exception as e:
        logger.error(f"Background persona sync failed: {e}")

async def ensure_agents_background(agent_factory):
    """Background task to ensure critical agents are registered."""
    try:
        # Simply verify agents are available - no additional registration needed
        available_agents = agent_factory.get_available_agents()
        logger.info(f"Background: Verified {len(available_agents)} agents are available: {available_agents}")

        # Check for critical agents (using correct persona IDs from YAML configs)
        critical_agents = ['concierge', 'composable-analysis-ai', 'composable-marketing-ai']
        missing_critical = [agent for agent in critical_agents if agent not in available_agents]

        if missing_critical:
            logger.warning(f"Background: Missing critical agents: {missing_critical}")
        else:
            logger.info("Background: All critical agents are available")

    except Exception as e:
        logger.error(f"Background agent verification failed: {e}")

async def ensure_analysis_components_background():
    """Background task to ensure analysis components are registered."""
    try:
        # Verify component system is working
        from agents.components import ComponentRegistry
        registered_components = ComponentRegistry.list_registered_components()
        logger.info(f"Background: Verified {len(registered_components)} components are registered")

        # Check for essential components
        essential_components = ['data_access', 'llm_processor', 'context_manager']
        available_components = [comp for comp in registered_components if any(essential in comp for essential in essential_components)]

        if available_components:
            logger.info(f"Background: Essential components available: {available_components}")
        else:
            logger.info("Background: Component system initialized (no essential components required)")

    except Exception as e:
        logger.error(f"Background component verification failed: {e}")

# Initialize services and database on startup
@asynccontextmanager
async def lifespan(_: FastAPI):
    """Lifespan event handler for services and database initialization."""
    startup_start_time = datetime.now()

    # Ensure configuration migration is complete
    logger.info("Ensuring configuration migration is complete...")
    migration_success = await ensure_migration_complete()
    if not migration_success:
        logger.warning("Configuration migration failed, continuing with fallback configuration")

    # Initialize error handling registry
    logger.info("Initializing error handling registry...")
    error_registry = get_error_registry()
    logger.info(f"Error registry initialized with {len(error_registry.handlers)} handlers")

    # Initialize database service
    logger.info("Initializing database service...")
    db_service = get_database_service()
    logger.info("Database service initialized")

    # Initialize database
    logger.info("Initializing database...")
    init_db()
    logger.info("Database initialized successfully.")

    # Initialize personas
    try:
        logger.info("Initializing personas...")

        # LangGraph WorkflowManager is already initialized and doesn't need database session factory
        logger.info("LangGraph WorkflowManager initialized successfully.")


        # Import here to avoid circular imports for persona sync
        from .utils.import_utils import ensure_backend_in_path
        ensure_backend_in_path()

        # Import the persona manager and agent factory using centralized import utility
        from .utils.import_utils import import_persona_manager, import_agent_factory
        import asyncio

        persona_manager_class = import_persona_manager()
        persona_manager = persona_manager_class()
        agent_factory = import_agent_factory()

        # Get database session
        from .database import get_db
        db = next(get_db())

        # Defer heavy operations to background tasks for faster startup
        asyncio.create_task(sync_personas_background(persona_manager, db))
        asyncio.create_task(ensure_agents_background(agent_factory))

        logger.info("Persona synchronization and agent registration deferred to background tasks")

        # Defer analysis components registration to background task
        asyncio.create_task(ensure_analysis_components_background())

        # Calculate and log startup time
        startup_duration = (datetime.now() - startup_start_time).total_seconds()
        logger.info(f"🚀 Startup completed in {startup_duration:.2f} seconds")

        # Track performance improvement
        if startup_duration < 10.0:
            logger.info("✅ Startup performance: EXCELLENT (< 10s)")
        elif startup_duration < 20.0:
            logger.info("✅ Startup performance: GOOD (< 20s)")
        else:
            logger.warning("⚠️ Startup performance: NEEDS OPTIMIZATION (> 20s)")

        logger.info("Personas initialized successfully.")
    except Exception as e:
        logger.error(f"Error initializing personas: {e}", exc_info=True)

    # Initialize Phase 1 performance optimizations
    try:
        logger.info("Initializing Phase 1 performance optimizations...")
        from .performance import initialize_performance_optimizations

        optimization_results = await initialize_performance_optimizations()

        if optimization_results["overall_status"] == "success":
            logger.info("✓ Phase 1 performance optimizations initialized successfully")
        else:
            logger.warning(f"⚠ Phase 1 performance optimizations partially initialized: {optimization_results}")

    except Exception as e:
        logger.error(f"Error initializing performance optimizations: {e}", exc_info=True)
        logger.info("Application will continue without performance optimizations")

    # Initialize LangGraph event handlers
    try:
        from agents.langgraph.events.event_bus import initialize_event_handlers
        await initialize_event_handlers()
        logger.info("✓ LangGraph event handlers initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize LangGraph event handlers: {e}")
        logger.info("Application will continue without tool execution indicators")

    # Initialize Enhanced Marketplace Components
    try:
        logger.info("Initializing enhanced marketplace components...")

        # Initialize industry specialization manager
        from agents.langgraph.industry.specialization_manager import IndustrySpecializationManager
        industry_manager = IndustrySpecializationManager()
        await industry_manager.initialize()
        logger.info("✓ Industry specialization manager initialized")

        # Initialize marketplace agent factory
        from agents.langgraph.core.marketplace_agent_factory import get_marketplace_agent_factory
        marketplace_factory = get_marketplace_agent_factory()
        if hasattr(marketplace_factory, 'initialize'):
            await marketplace_factory.initialize()
        logger.info("✓ Marketplace agent factory initialized")

        # Initialize plugin manager
        from agents.langgraph.plugins.plugin_manager import AgentPluginManager
        plugin_manager = AgentPluginManager()
        await plugin_manager.initialize()
        logger.info("✓ Agent plugin manager initialized")

        # Initialize hierarchical message manager
        from agents.langgraph.messaging.hierarchical_message_manager import HierarchicalMessageManager
        message_manager = HierarchicalMessageManager()
        await message_manager.initialize()
        logger.info("✓ Hierarchical message manager initialized")

        logger.info("✓ Enhanced marketplace system initialized successfully")

    except Exception as e:
        logger.error(f"Failed to initialize enhanced marketplace components: {e}")
        logger.info("Application will continue with basic functionality")

    yield

# Create FastAPI app
app = FastAPI(
    title="Datagenius API",
    description="API for the Datagenius application",
    version="1.0.0",
    lifespan=lifespan
)

# Configure CORS
# Ensure we have proper CORS origins for development
cors_origins = config.CORS_ORIGINS
if cors_origins == ["*"] or "*" in cors_origins:
    cors_origins = ["*"]
else:
    # Add common development origins if not present
    dev_origins = [
        "http://localhost:3000",
        "http://localhost:5173",
        "http://127.0.0.1:3000",
        "http://127.0.0.1:5173",
        "http://localhost:8080",
        "http://127.0.0.1:8080"
    ]
    for origin in dev_origins:
        if origin not in cors_origins:
            cors_origins.append(origin)

logger.info(f"Configuring CORS with origins: {cors_origins}")
app.add_middleware(
    CORSMiddleware,
    allow_origins=cors_origins,
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
    allow_headers=["*"],
    expose_headers=["Content-Disposition"],
    max_age=600,  # 10 minutes
)

# Add error handling middleware
from .errors.error_handler_registry import ErrorHandlerMiddleware
app.add_middleware(ErrorHandlerMiddleware)
logger.info("Error handling middleware configured")

# Add correlation ID middleware
from .errors.correlation_context import CorrelationMiddleware
app.add_middleware(CorrelationMiddleware)
logger.info("Correlation ID middleware configured")

# Add Unified Security Middleware
import os
environment = os.getenv('ENVIRONMENT', 'development')

# Configure and add the unified security middleware
security_config = UNIFIED_SECURITY_CONFIG.get(environment, UNIFIED_SECURITY_CONFIG['development'])
security_middleware_instance = UnifiedSecurityMiddleware(app, config=security_config)
app.add_middleware(UnifiedSecurityMiddleware, config=security_config)
logger.info(f"Unified security middleware configured for {environment} environment")

# Include routers
app.include_router(auth_router)
app.include_router(chat_router)
app.include_router(agents_router)
app.include_router(files_router)

# Create a special route for purchased personas
from fastapi import Depends
from sqlalchemy.orm import Session
from typing import List
from .database import get_db
from .auth import get_current_active_user
from .models.auth import User
from .services import persona_service

@app.get("/personas/purchased", response_model=List[str], tags=["Personas"])
async def get_purchased_personas(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Get a list of persona IDs that the user has purchased.
    """
    logger.info(f"User {current_user.id} requested purchased personas")
    try:
        # Get purchased personas using the service
        persona_ids = persona_service.get_user_purchased_personas(db, current_user.id)
        logger.info(f"Found {len(persona_ids)} purchased personas for user {current_user.id}: {persona_ids}")
        return persona_ids
    except Exception as e:
        logger.error(f"Error getting purchased personas for user {current_user.id}: {str(e)}", exc_info=True)
        # Return empty list instead of failing
        return []

# Include the rest of the routers
app.include_router(personas_router)
app.include_router(providers_router)
app.include_router(data_sources_router)
app.include_router(cart_router)
app.include_router(purchases_router)
app.include_router(admin_router)
app.include_router(health_router)
app.include_router(models_router)
app.include_router(document_query_router)
app.include_router(docx_router)
# Dashboard metrics router removed
app.include_router(dashboards_router)
app.include_router(data_access_router)
app.include_router(analytics_widgets_router)
app.include_router(reports_router)
app.include_router(notifications_router)
app.include_router(feedback_router)
app.include_router(pricing_router)
app.include_router(search_router)
app.include_router(dashboard_customization_router)
app.include_router(embedding_config_router)
app.include_router(quick_actions_router)
app.include_router(chunking_performance_router)
app.include_router(business_context_router)
app.include_router(business_profiles_router)
app.include_router(business_profile_autofill_router)
app.include_router(profile_recommendations_router)
app.include_router(data_source_intelligence_router)
app.include_router(business_profile_analytics_router)
app.include_router(realtime_context_router)
app.include_router(cross_agent_intelligence_router)
app.include_router(template_management_router)
app.include_router(dashboard_websocket_router)
app.include_router(dashboard_alt_router)
app.include_router(performance_router)
app.include_router(enhanced_personas_router)
app.include_router(mcp_servers_router)

# WebSocket endpoints
# Dashboard metrics WebSocket endpoint removed

# Root endpoint
@app.get("/", tags=["Root"])
async def root():
    """Root endpoint."""
    return {
        "message": "Welcome to the Datagenius API",
        "version": "1.0.0",
        "docs_url": "/docs",
    }

# Health check endpoint
@app.get("/health", tags=["Health"])
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy"}

# Debug endpoints for development
if environment == "development":
    @app.get("/debug/security/ip-status/{ip}", tags=["Debug"])
    async def get_ip_security_status(ip: str):
        """Get security status for an IP address (development only)."""
        return security_middleware_instance.get_ip_status(ip)

    @app.post("/debug/security/unblock-ip/{ip}", tags=["Debug"])
    async def unblock_ip_address(ip: str):
        """Unblock an IP address (development only)."""
        success = security_middleware_instance.unblock_ip(ip)
        return {"success": success, "message": f"IP {ip} {'unblocked' if success else 'was not blocked'}"}

# Error handlers
from fastapi.exceptions import RequestValidationError
from pydantic import ValidationError

@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request, exc):
    """Handle validation errors with detailed logging."""
    try:
        body = await request.body()
        body_str = body.decode('utf-8') if body else "No body"
    except Exception as e:
        body_str = f"Error reading body: {e}"

    logger.error(f"Validation error on {request.method} {request.url}")
    logger.error(f"Validation errors: {exc.errors()}")
    logger.error(f"Request body: {body_str}")

    return JSONResponse(
        status_code=422,
        content={"detail": "Validation error", "errors": exc.errors()},
    )

@app.exception_handler(ValidationError)
async def pydantic_validation_exception_handler(request, exc):
    """Handle Pydantic validation errors with detailed logging."""
    logger.error(f"Pydantic validation error on {request.method} {request.url}")
    logger.error(f"Pydantic validation errors: {exc.errors()}")

    return JSONResponse(
        status_code=422,
        content={"detail": "Pydantic validation error", "errors": exc.errors()},
    )

@app.exception_handler(Exception)
async def global_exception_handler(_, exc):
    """Global exception handler."""
    logger.error(f"Unhandled exception: {str(exc)}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={"detail": "An unexpected error occurred. Please try again later."},
    )
